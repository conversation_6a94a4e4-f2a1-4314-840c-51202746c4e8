# VHRS Migration Structure Documentation

## Email System Migrations

The email functionality has been split into two separate migration files for better organization and maintainability:

### 1. Email Functionality Migration
**File**: `20250527220000_add_email_functionality.sql`

**Purpose**: Core email system functionality

**Contents**:
- ✅ `support_ticket_replies` table creation
- ✅ Email threading fields (`message_id`, `in_reply_to`, `email_references`)
- ✅ RLS policies for the replies table
- ✅ `check_user_permission()` RPC function
- ✅ `send_support_ticket_reply()` RPC function with threading support
- ✅ Automated status updates (open → in_progress)

**Key Features**:
- Email threading headers for conversation continuity
- Automated ticket status updates when agents reply
- Permission-based access control
- Complete audit trail of email communications

### 2. Permission System Update Migration
**File**: `20250527221000_exclude_support_ticket_replies_from_permissions.sql`

**Purpose**: Update permission system to exclude support_ticket_replies table

**Contents**:
- ✅ Updated `generate_permissions()` function with excluded tables list
- ✅ Updated `handle_table_creation()` event trigger
- ✅ Cleanup of any existing support_ticket_replies permissions
- ✅ Documentation of exclusion reasoning

**Excluded Tables List**:
```sql
excluded_tables TEXT[] := ARRAY[
  -- System tables
  'schema_migrations',
  'spatial_ref_sys', 
  'pg_stat_statements',

  -- Tables managed through special permissions
  'permissions',
  'role_permissions',
  'user_roles',
  'profiles',
  'data_submissions',
  
  -- Tables that use parent table permissions
  'support_ticket_replies'  -- Uses support_tickets permissions
];
```

## Migration Timeline

```
20250527211014 - Add Support Tickets Table
    ↓
20250527220000 - Add Email Functionality
    ↓
20250527221000 - Exclude Support Ticket Replies from Permissions
```

## Why This Structure?

### **Separation of Concerns**
- **Email Migration**: Focuses purely on email functionality
- **Permission Migration**: Handles permission system updates

### **Maintainability**
- Changes to permission exclusions don't affect email functionality
- Email features can be modified independently
- Clear migration history and purpose

### **Rollback Safety**
- Each migration can be rolled back independently
- Permission changes are isolated from core functionality
- Easier to debug issues in specific areas

## Database Schema Result

### support_ticket_replies Table
```sql
CREATE TABLE support_ticket_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE NOT NULL,
  sender_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  message_id TEXT,           -- Email Message-ID for threading
  in_reply_to TEXT,          -- In-Reply-To header for threading  
  email_references TEXT,     -- References header for threading
  sent_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Permission Inheritance
- ❌ **No direct permissions**: `support_ticket_replies:create`, `support_ticket_replies:view`, etc.
- ✅ **Uses parent permissions**: `support_tickets:edit`, `support_tickets:view`
- ✅ **Logical grouping**: Email replies are part of ticket management

## RPC Functions Added

### 1. check_user_permission(user_id, permission_code)
- Validates user permissions for any given permission code
- Used by email functions to ensure proper access control
- Returns boolean result

### 2. send_support_ticket_reply(ticket_id, reply_subject, reply_message)
- Prepares email reply with threading information
- Automatically updates ticket status (open → in_progress)
- Returns complete threading metadata for email sending
- Handles conversation continuity

## Migration Application Results

```
✅ Applying migration 20250527220000_add_email_functionality.sql...
✅ Applying migration 20250527221000_exclude_support_ticket_replies_from_permissions.sql...
   NOTICE: Excluded support_ticket_replies table from automatic permission generation
   NOTICE: This table uses support_tickets permissions for access control
```

## Testing the Structure

### Verify Exclusion Works
```sql
-- This should return no results
SELECT * FROM permissions WHERE code LIKE 'support_ticket_replies:%';

-- This should show support_tickets permissions
SELECT * FROM permissions WHERE code LIKE 'support_tickets:%';
```

### Test Email Functionality
```sql
-- Test the RPC function
SELECT send_support_ticket_reply(
  'ticket-uuid-here'::uuid,
  'Re: Your Support Request',
  'Thank you for contacting us...'
);
```

## Future Considerations

### Adding New Excluded Tables
- Add to the `excluded_tables` array in the permission migration
- Create a new migration if needed for complex exclusions
- Document the reasoning for exclusion

### Email System Extensions
- Additional email features go in new email-specific migrations
- Keep permission changes separate from functionality changes
- Maintain clear migration purposes and documentation

## Best Practices Demonstrated

1. **Single Responsibility**: Each migration has one clear purpose
2. **Logical Grouping**: Related functionality stays together
3. **Clear Naming**: Migration names indicate their purpose
4. **Documentation**: Each migration includes comments explaining its purpose
5. **Rollback Safety**: Migrations can be undone independently
6. **Testing**: Each migration can be tested in isolation
