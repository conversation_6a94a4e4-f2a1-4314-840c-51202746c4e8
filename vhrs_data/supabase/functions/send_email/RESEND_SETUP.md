# Resend Email Provider Setup Guide

This guide provides detailed instructions for setting up <PERSON><PERSON><PERSON> as the email provider for the VHRS support ticket system.

## Overview

Resend is a modern email API service that provides excellent deliverability, email threading support, and developer-friendly APIs. This implementation supports:

- **Email Threading**: Full conversation support with Message-ID, In-Reply-To, References headers
- **Professional Delivery**: High deliverability rates and spam protection
- **Real-time Tracking**: Email delivery status and tracking
- **Domain Authentication**: SPF, DKIM, and DMARC support

## Prerequisites

1. **Resend Account**: Sign up at [resend.com](https://resend.com)
2. **Domain Verification**: You'll need a domain to send emails from
3. **DNS Access**: To configure domain authentication records

## Step 1: Create Resend Account

1. Go to [resend.com](https://resend.com) and sign up
2. Verify your email address
3. Complete the onboarding process

## Step 2: Domain Setup

### Add Your Domain

1. In the Resend dashboard, go to **Domains**
2. Click **Add Domain**
3. Enter your domain (e.g., `yourdomain.com`)
4. Click **Add**

### Configure DNS Records

Resend will provide DNS records to add to your domain:

```dns
# SPF Record (TXT)
Name: @
Value: v=spf1 include:_spf.resend.com ~all

# DKIM Record (TXT)
Name: resend._domainkey
Value: [Provided by Resend - unique per domain]

# DMARC Record (TXT) - Optional but recommended
Name: _dmarc
Value: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>
```

### Verify Domain

1. Add all DNS records to your domain
2. Wait for DNS propagation (up to 24 hours)
3. Click **Verify** in the Resend dashboard
4. Status should change to **Verified**

## Step 3: Generate API Key

1. In Resend dashboard, go to **API Keys**
2. Click **Create API Key**
3. Choose **Sending access** (recommended for production)
4. Name it something like "VHRS Production" or "VHRS Development"
5. Copy the API key (starts with `re_`)

## Step 4: Environment Configuration

### Local Development (.env.local)

Create or update your environment file:

```bash
# Email Provider Configuration
EMAIL_PROVIDER=resend

# Resend Configuration
RESEND_API_KEY=re_your_api_key_here

# Email Settings
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
MAIL_REPLY_TO=<EMAIL>
```

### Production Environment

Set the same environment variables in your production environment:

- **Supabase Cloud**: Add to Project Settings > Environment Variables
- **Vercel**: Add to Project Settings > Environment Variables
- **Other platforms**: Follow platform-specific instructions

## Step 5: Test Configuration

### Test Email Sending

1. Start your local development environment:
   ```bash
   cd vhrs_data
   supabase start
   supabase functions serve
   ```

2. Test the email function:
   ```bash
   curl -X POST http://localhost:54321/functions/v1/send_email \
     -H "Authorization: Bearer YOUR_SUPABASE_ANON_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "to": "<EMAIL>",
       "subject": "Test Email",
       "message": "This is a test email from VHRS."
     }'
   ```

### Verify in Resend Dashboard

1. Go to **Logs** in Resend dashboard
2. You should see the sent email
3. Check delivery status and any errors

## Step 6: Support Ticket Integration

The email function automatically integrates with support tickets when called with threading information:

```javascript
// Frontend usage (already implemented)
const { data } = await supabase.rpc('send_support_ticket_reply', {
  p_ticket_id: 'uuid-here',
  reply_subject: 'Re: Your Support Request',
  reply_message: 'Thank you for contacting us...'
});

// Then send email with threading
await supabase.functions.invoke('send_email', {
  body: {
    to: '<EMAIL>',
    subject: 'Re: Your Support Request',
    message: 'Thank you for contacting us...',
    ticketId: 'uuid-here',
    replyToTicket: true,
    threading: data.threading
  }
});
```

## Email Threading Explained

### How It Works

Resend supports email threading through standard email headers:

1. **Message-ID**: Unique identifier for each email
   - Format: `ticket-{uuid}-{timestamp}@vhrs.system`

2. **In-Reply-To**: References the Message-ID of the email being replied to
   - Links this email to its immediate parent

3. **References**: Contains all Message-IDs in the conversation thread
   - Maintains the complete conversation history

### Threading Example

```
Original Ticket (conceptual):
Message-ID: <EMAIL>

First Reply:
Message-ID: <EMAIL>
In-Reply-To: <EMAIL>
References: <EMAIL>

Second Reply:
Message-ID: <EMAIL>
In-Reply-To: <EMAIL>
References: <EMAIL> <EMAIL>
```

## Troubleshooting

### Common Issues

1. **Domain Not Verified**
   - Check DNS records are correctly added
   - Wait for DNS propagation (up to 24 hours)
   - Use DNS checker tools to verify records

2. **API Key Invalid**
   - Ensure API key starts with `re_`
   - Check key has sending permissions
   - Regenerate key if necessary

3. **Emails Not Sending**
   - Check Resend logs for errors
   - Verify FROM email matches verified domain
   - Check rate limits (Resend free tier: 100 emails/day)

4. **Threading Not Working**
   - Verify headers are being set correctly
   - Check email client supports threading
   - Gmail and Outlook support threading best

### Debug Mode

Enable debug logging by checking the function logs:

```bash
# View function logs
supabase functions logs --follow

# Or check specific function
supabase functions logs send_email --follow
```

## Rate Limits

### Resend Limits

- **Free Tier**: 100 emails/day, 3,000 emails/month
- **Pro Tier**: 50,000 emails/month, higher daily limits
- **Enterprise**: Custom limits

### VHRS Rate Limiting

The function includes built-in rate limiting:
- **10 emails per minute per user**
- Prevents abuse and spam
- Configurable in `index.ts`

## Security Best Practices

1. **API Key Security**
   - Never commit API keys to version control
   - Use environment variables only
   - Rotate keys regularly

2. **Domain Authentication**
   - Always set up SPF, DKIM, and DMARC
   - Monitor domain reputation
   - Use dedicated sending domain

3. **Content Security**
   - Validate email content
   - Sanitize user inputs
   - Implement permission checks

## Monitoring and Analytics

### Resend Dashboard

Monitor email performance:
- **Delivery Rates**: Track successful deliveries
- **Bounce Rates**: Monitor bounced emails
- **Spam Reports**: Check spam complaints
- **Click Tracking**: Monitor engagement (if enabled)

### Database Logging

All sent emails are logged in the `support_ticket_replies` table:
- Email content and recipients
- Resend email ID for tracking
- Threading information
- Timestamps and sender information

## Migration from SMTP

If migrating from SMTP to Resend:

1. **Test in Development**: Verify all functionality works
2. **Update Environment**: Change `EMAIL_PROVIDER=resend`
3. **Monitor Delivery**: Check Resend logs for issues
4. **Gradual Rollout**: Consider feature flags for gradual migration

## Support

- **Resend Documentation**: [resend.com/docs](https://resend.com/docs)
- **Resend Support**: Available through dashboard
- **VHRS Issues**: Check function logs and database entries
