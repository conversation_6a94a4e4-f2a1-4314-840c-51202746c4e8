// Gmail SMTP email provider implementation using denomailer
import { SMTPClient } from 'denomailer'

export interface SMTPConfig {
  host: string
  port: number
  username: string
  password: string
  secure: boolean
  fromName: string
  fromEmail: string
  replyTo?: string
}

export interface EmailRequest {
  to: string
  subject: string
  message: string
  threading?: {
    messageId: string
    inReplyTo: string
    references: string
  }
}

export interface EmailResult {
  success: boolean
  id?: string
  error?: string
}

export async function sendEmailSMTP(config: SMTPConfig, emailData: EmailRequest): Promise<EmailResult> {
  try {
    console.log('=== SENDING EMAIL VIA GMAIL SMTP (DENOMAILER) ===')
    console.log('From:', `${config.fromName} <${config.fromEmail}>`)
    console.log('To:', emailData.to)
    console.log('Subject:', emailData.subject)

    // Generate unique message ID for threading
    const messageId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}@vhrs.system`

    // Create SMTP client with SSL (port 465)
    const client = new SMTPClient({
      connection: {
        hostname: config.host,
        port: config.port,
        tls: config.secure, // Use SSL/TLS directly
        auth: {
          username: config.username,
          password: config.password,
        },
      },
    })

    // Prepare email headers
    const headers: Record<string, string> = {
      'Message-ID': `<${messageId}>`,
      'Date': new Date().toUTCString(),
      'MIME-Version': '1.0',
      'Content-Type': 'text/plain; charset=utf-8',
    }

    // Add Reply-To if configured
    if (config.replyTo) {
      headers['Reply-To'] = config.replyTo
    }

    // Add threading headers if this is a reply
    if (emailData.threading) {
      console.log('Threading Headers:')
      console.log('  Message-ID:', emailData.threading.messageId)
      console.log('  In-Reply-To:', emailData.threading.inReplyTo)
      console.log('  References:', emailData.threading.references)

      if (emailData.threading.inReplyTo) {
        headers['In-Reply-To'] = `<${emailData.threading.inReplyTo}>`
      }
      if (emailData.threading.references) {
        headers['References'] = emailData.threading.references
      }
    }

    // Send email using denomailer
    await client.send({
      from: `${config.fromName} <${config.fromEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      content: emailData.message,
      headers: headers,
    })

    console.log('Email sent successfully via Gmail SMTP (denomailer)')
    console.log('=== EMAIL SENT SUCCESSFULLY ===')

    return {
      success: true,
      id: messageId
    }

  } catch (error) {
    console.error('Error sending email via Gmail SMTP:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown SMTP error occurred'
    }
  }
}
