# Send Email Function

Sends support ticket email replies with threading support.

## Configuration

Set `EMAIL_PROVIDER=smtp` or `EMAIL_PROVIDER=resend` plus provider-specific variables:

**SMTP (Gmail):**
```bash
EMAIL_PROVIDER=smtp
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_FROM_EMAIL=<EMAIL>
```

**Resend:**
```bash
EMAIL_PROVIDER=resend
RESEND_API_KEY=re_your_api_key
MAIL_FROM_EMAIL=<EMAIL>
```

## Features

- Email threading for support ticket conversations
- Permission-based access (`support_tickets:edit`)
- Rate limiting (10/min per user)
- Automatic database logging

## Files

- `index.ts` - Main function with auth and routing
- `providers/` - Email provider implementations
- `types.ts` - TypeScript interfaces
