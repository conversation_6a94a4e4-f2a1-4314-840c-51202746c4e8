# Email Function

Email sending function with support for multiple providers.

## Provider Switching

Switch between email providers using the `EMAIL_PROVIDER` environment variable:

```bash
# Use Gmail SMTP (current)
EMAIL_PROVIDER=smtp

# Use Resend API
EMAIL_PROVIDER=resend
```

## Configuration

### Gmail SMTP

```bash
EMAIL_PROVIDER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_SECURE=true
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
MAIL_REPLY_TO=<EMAIL>
```

### Resend API

```bash
EMAIL_PROVIDER=resend
RESEND_API_KEY=re_your_api_key
MAIL_FROM_NAME=VHRS Support
MAIL_FROM_EMAIL=<EMAIL>
MAIL_REPLY_TO=<EMAIL>
```

## Features

- **Email Threading**: Full conversation support with Message-ID, In-Reply-To, References headers
- **Rate Limiting**: 10 emails per minute per user
- **Permission Checks**: Requires `support_tickets:edit` permission
- **Database Logging**: Automatic logging of support ticket replies
- **Error Handling**: Comprehensive error reporting

## Adding New Providers

1. Create provider file in `providers/` folder
2. Implement function matching `EmailResult` interface
3. Add configuration type to `types.ts`
4. Update routing logic in `index.ts`

## Architecture

- `index.ts` - Main router, authentication, configuration
- `types.ts` - Shared TypeScript interfaces
- `providers/resend.ts` - Resend API implementation
- `providers/smtp.ts` - Gmail SMTP implementation
